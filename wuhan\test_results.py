#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析结果脚本
快速验证回归分析的主要结果
"""

from data_preprocessing import WuhanDataPreprocessor
from regression_modeling import MultipleLinearRegressionAnalyzer

def test_analysis_results():
    """测试分析结果"""
    print("=" * 60)
    print("武汉项目回归分析结果验证")
    print("=" * 60)
    
    # 加载和预处理数据
    preprocessor = WuhanDataPreprocessor('data_wuhan.db')
    raw_data = preprocessor.load_data()
    processed_data = preprocessor.preprocess_data()
    X, y1, y2 = preprocessor.get_model_data()
    
    print(f"数据基本信息:")
    print(f"  - 样本数: {len(processed_data)}")
    print(f"  - 特征数: {X.shape[1]}")
    print(f"  - 特征变量: {list(X.columns)}")
    print(f"  - 目标变量1 (大散人次): 均值={y1.mean():.1f}, 标准差={y1.std():.1f}")
    print(f"  - 目标变量2 (夜场人次): 均值={y2.mean():.1f}, 标准差={y2.std():.1f}")
    
    # 构建回归模型
    modeler = MultipleLinearRegressionAnalyzer(X, y1, y2)
    results = modeler.run_full_analysis()
    
    print(f"\n回归模型结果:")
    
    # 大散人次模型
    model1_summary = results['model1']['model_summary']
    print(f"\n【大散人次回归模型】")
    print(f"  - R² = {model1_summary['r_squared']:.4f}")
    print(f"  - 调整R² = {model1_summary['adj_r_squared']:.4f}")
    print(f"  - F统计量 = {model1_summary['f_statistic']:.2f}")
    print(f"  - F检验p值 = {model1_summary['f_pvalue']:.6f}")
    print(f"  - RMSE = {model1_summary['rmse']:.2f}")
    print(f"  - 模型显著性: {'显著' if model1_summary['f_pvalue'] < 0.05 else '不显著'}")
    
    # 夜场人次模型
    model2_summary = results['model2']['model_summary']
    print(f"\n【夜场人次回归模型】")
    print(f"  - R² = {model2_summary['r_squared']:.4f}")
    print(f"  - 调整R² = {model2_summary['adj_r_squared']:.4f}")
    print(f"  - F统计量 = {model2_summary['f_statistic']:.2f}")
    print(f"  - F检验p值 = {model2_summary['f_pvalue']:.6f}")
    print(f"  - RMSE = {model2_summary['rmse']:.2f}")
    print(f"  - 模型显著性: {'显著' if model2_summary['f_pvalue'] < 0.05 else '不显著'}")
    
    # 显著性系数
    print(f"\n【显著性系数分析】")
    
    print(f"\n大散人次模型显著系数:")
    for var, coef_info in results['model1']['coefficients'].items():
        if coef_info['p_value'] < 0.05:
            print(f"  - {var}: 系数={coef_info['value']:.4f}, p值={coef_info['p_value']:.4f}")
    
    print(f"\n夜场人次模型显著系数:")
    for var, coef_info in results['model2']['coefficients'].items():
        if coef_info['p_value'] < 0.05:
            print(f"  - {var}: 系数={coef_info['value']:.4f}, p值={coef_info['p_value']:.4f}")
    
    print(f"\n" + "=" * 60)
    print("分析结果验证完成")
    print("=" * 60)

if __name__ == "__main__":
    test_analysis_results()
