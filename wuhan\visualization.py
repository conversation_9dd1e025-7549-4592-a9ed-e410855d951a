#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
武汉项目数据可视化模块
负责创建各种统计图表和可视化分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.figure_factory as ff
from scipy import stats
import logging
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataVisualizationGenerator:
    """数据可视化生成器"""
    
    def __init__(self, data: pd.DataFrame, analysis_results: Dict = None, model_results: Dict = None):
        """
        初始化可视化生成器
        
        Args:
            data: 原始数据DataFrame
            analysis_results: 探索性分析结果
            model_results: 回归模型结果
        """
        self.data = data.copy()
        self.analysis_results = analysis_results or {}
        self.model_results = model_results or {}
        self.figures = {}
        
        logger.info(f"初始化可视化生成器，数据形状: {self.data.shape}")
    
    def create_descriptive_plots(self) -> Dict[str, Any]:
        """
        创建描述性统计图表
        
        Returns:
            描述性统计图表字典
        """
        logger.info("创建描述性统计图表...")
        
        plots = {}
        
        # 1. 目标变量分布图
        target_vars = ['大散人次', '夜场人次']
        
        fig_dist = make_subplots(
            rows=2, cols=2,
            subplot_titles=['大散人次分布', '大散人次箱线图', '夜场人次分布', '夜场人次箱线图'],
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        for i, var in enumerate(target_vars):
            if var in self.data.columns:
                data_series = self.data[var]
                
                # 直方图
                fig_dist.add_trace(
                    go.Histogram(x=data_series, name=f'{var}分布', nbinsx=30),
                    row=i+1, col=1
                )
                
                # 箱线图
                fig_dist.add_trace(
                    go.Box(y=data_series, name=f'{var}箱线图'),
                    row=i+1, col=2
                )
        
        fig_dist.update_layout(
            title="目标变量分布分析",
            height=600,
            showlegend=False
        )
        
        plots['target_distribution'] = fig_dist
        
        # 2. 特征变量分布图
        feature_vars = ['大散客单', '夜场客单', 'Holiday_Day_Index']
        
        fig_features = make_subplots(
            rows=1, cols=3,
            subplot_titles=feature_vars
        )
        
        for i, var in enumerate(feature_vars):
            if var in self.data.columns:
                fig_features.add_trace(
                    go.Histogram(x=self.data[var], name=var, nbinsx=20),
                    row=1, col=i+1
                )
        
        fig_features.update_layout(
            title="特征变量分布分析",
            height=400,
            showlegend=False
        )
        
        plots['feature_distribution'] = fig_features
        
        # 3. 星期效应分析
        weekday_cols = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
        weekday_labels = []
        
        for idx, row in self.data.iterrows():
            found_weekday = False
            for col in weekday_cols:
                if col in self.data.columns and row[col] == 1:
                    weekday_labels.append(col)
                    found_weekday = True
                    break
            if not found_weekday:
                weekday_labels.append('星期日')
        
        data_with_weekday = self.data.copy()
        data_with_weekday['星期'] = weekday_labels
        
        fig_weekday = make_subplots(
            rows=1, cols=2,
            subplot_titles=['大散人次星期效应', '夜场人次星期效应']
        )
        
        for i, target in enumerate(target_vars):
            if target in data_with_weekday.columns:
                weekday_stats = data_with_weekday.groupby('星期')[target].agg(['mean', 'std']).reset_index()
                
                fig_weekday.add_trace(
                    go.Bar(
                        x=weekday_stats['星期'],
                        y=weekday_stats['mean'],
                        error_y=dict(type='data', array=weekday_stats['std']),
                        name=f'{target}均值'
                    ),
                    row=1, col=i+1
                )
        
        fig_weekday.update_layout(
            title="星期效应分析",
            height=400,
            showlegend=False
        )
        
        plots['weekday_effect'] = fig_weekday
        
        self.figures.update(plots)
        logger.info("描述性统计图表创建完成")
        
        return plots
    
    def create_correlation_plots(self) -> Dict[str, Any]:
        """
        创建相关性分析图表
        
        Returns:
            相关性分析图表字典
        """
        logger.info("创建相关性分析图表...")
        
        plots = {}
        
        # 1. 相关性热力图
        numeric_vars = ['大散人次', '夜场人次', '大散客单', '夜场客单', 'Holiday_Day_Index']
        corr_data = self.data[numeric_vars].corr()
        
        fig_corr = go.Figure(data=go.Heatmap(
            z=corr_data.values,
            x=corr_data.columns,
            y=corr_data.columns,
            colorscale='RdBu',
            zmid=0,
            text=np.round(corr_data.values, 3),
            texttemplate="%{text}",
            textfont={"size": 10},
            hoverongaps=False
        ))
        
        fig_corr.update_layout(
            title="变量相关性热力图",
            height=500,
            width=600
        )
        
        plots['correlation_heatmap'] = fig_corr
        
        # 2. 散点图矩阵
        fig_scatter = px.scatter_matrix(
            self.data[numeric_vars],
            title="变量散点图矩阵",
            height=800
        )
        
        plots['scatter_matrix'] = fig_scatter
        
        # 3. 目标变量与主要特征的散点图
        fig_target_scatter = make_subplots(
            rows=2, cols=2,
            subplot_titles=['大散人次 vs 大散客单', '大散人次 vs Holiday_Day_Index',
                          '夜场人次 vs 夜场客单', '夜场人次 vs Holiday_Day_Index']
        )
        
        # 大散人次 vs 大散客单
        if all(col in self.data.columns for col in ['大散人次', '大散客单']):
            fig_target_scatter.add_trace(
                go.Scatter(x=self.data['大散客单'], y=self.data['大散人次'], 
                          mode='markers', name='大散人次 vs 大散客单'),
                row=1, col=1
            )
        
        # 大散人次 vs Holiday_Day_Index
        if all(col in self.data.columns for col in ['大散人次', 'Holiday_Day_Index']):
            fig_target_scatter.add_trace(
                go.Scatter(x=self.data['Holiday_Day_Index'], y=self.data['大散人次'], 
                          mode='markers', name='大散人次 vs Holiday_Day_Index'),
                row=1, col=2
            )
        
        # 夜场人次 vs 夜场客单
        if all(col in self.data.columns for col in ['夜场人次', '夜场客单']):
            fig_target_scatter.add_trace(
                go.Scatter(x=self.data['夜场客单'], y=self.data['夜场人次'], 
                          mode='markers', name='夜场人次 vs 夜场客单'),
                row=2, col=1
            )
        
        # 夜场人次 vs Holiday_Day_Index
        if all(col in self.data.columns for col in ['夜场人次', 'Holiday_Day_Index']):
            fig_target_scatter.add_trace(
                go.Scatter(x=self.data['Holiday_Day_Index'], y=self.data['夜场人次'], 
                          mode='markers', name='夜场人次 vs Holiday_Day_Index'),
                row=2, col=2
            )
        
        fig_target_scatter.update_layout(
            title="目标变量与主要特征散点图",
            height=600,
            showlegend=False
        )
        
        plots['target_feature_scatter'] = fig_target_scatter
        
        self.figures.update(plots)
        logger.info("相关性分析图表创建完成")
        
        return plots
    
    def create_regression_plots(self) -> Dict[str, Any]:
        """
        创建回归分析图表
        
        Returns:
            回归分析图表字典
        """
        if not self.model_results:
            logger.warning("没有模型结果，跳过回归图表创建")
            return {}
        
        logger.info("创建回归分析图表...")
        
        plots = {}
        
        # 1. 实际值 vs 预测值图
        fig_pred = make_subplots(
            rows=1, cols=2,
            subplot_titles=['大散人次模型：实际值 vs 预测值', '夜场人次模型：实际值 vs 预测值']
        )
        
        if 'model1' in self.model_results:
            actual1 = self.model_results['model1']['actual_values']
            pred1 = self.model_results['model1']['predictions']
            
            fig_pred.add_trace(
                go.Scatter(x=actual1, y=pred1, mode='markers', name='大散人次'),
                row=1, col=1
            )
            
            # 添加理想线 (y=x)
            min_val1, max_val1 = min(actual1), max(actual1)
            fig_pred.add_trace(
                go.Scatter(x=[min_val1, max_val1], y=[min_val1, max_val1], 
                          mode='lines', name='理想线', line=dict(color='red', dash='dash')),
                row=1, col=1
            )
        
        if 'model2' in self.model_results:
            actual2 = self.model_results['model2']['actual_values']
            pred2 = self.model_results['model2']['predictions']
            
            fig_pred.add_trace(
                go.Scatter(x=actual2, y=pred2, mode='markers', name='夜场人次'),
                row=1, col=2
            )
            
            # 添加理想线
            min_val2, max_val2 = min(actual2), max(actual2)
            fig_pred.add_trace(
                go.Scatter(x=[min_val2, max_val2], y=[min_val2, max_val2], 
                          mode='lines', name='理想线', line=dict(color='red', dash='dash')),
                row=1, col=2
            )
        
        fig_pred.update_layout(
            title="回归模型预测效果",
            height=400,
            showlegend=False
        )
        
        plots['prediction_plot'] = fig_pred
        
        # 2. 残差图
        fig_residual = make_subplots(
            rows=2, cols=2,
            subplot_titles=['大散人次残差图', '大散人次残差分布', '夜场人次残差图', '夜场人次残差分布']
        )
        
        if 'model1' in self.model_results:
            pred1 = self.model_results['model1']['predictions']
            residuals1 = self.model_results['model1']['residuals']
            
            # 残差散点图
            fig_residual.add_trace(
                go.Scatter(x=pred1, y=residuals1, mode='markers', name='大散人次残差'),
                row=1, col=1
            )
            
            # 添加零线
            fig_residual.add_hline(y=0, line_dash="dash", line_color="red", row=1, col=1)
            
            # 残差直方图
            fig_residual.add_trace(
                go.Histogram(x=residuals1, name='大散人次残差分布', nbinsx=20),
                row=1, col=2
            )
        
        if 'model2' in self.model_results:
            pred2 = self.model_results['model2']['predictions']
            residuals2 = self.model_results['model2']['residuals']
            
            # 残差散点图
            fig_residual.add_trace(
                go.Scatter(x=pred2, y=residuals2, mode='markers', name='夜场人次残差'),
                row=2, col=1
            )
            
            # 添加零线
            fig_residual.add_hline(y=0, line_dash="dash", line_color="red", row=2, col=1)
            
            # 残差直方图
            fig_residual.add_trace(
                go.Histogram(x=residuals2, name='夜场人次残差分布', nbinsx=20),
                row=2, col=2
            )
        
        fig_residual.update_layout(
            title="残差分析",
            height=600,
            showlegend=False
        )
        
        plots['residual_plot'] = fig_residual
        
        # 3. QQ图（正态性检验）
        fig_qq = make_subplots(
            rows=1, cols=2,
            subplot_titles=['大散人次残差QQ图', '夜场人次残差QQ图']
        )
        
        if 'model1' in self.model_results:
            residuals1 = np.array(self.model_results['model1']['residuals'])
            qq_data1 = stats.probplot(residuals1, dist="norm")
            
            fig_qq.add_trace(
                go.Scatter(x=qq_data1[0][0], y=qq_data1[0][1], mode='markers', name='大散人次QQ'),
                row=1, col=1
            )
            
            # 添加理论线
            fig_qq.add_trace(
                go.Scatter(x=qq_data1[0][0], y=qq_data1[1][1] + qq_data1[1][0] * qq_data1[0][0], 
                          mode='lines', name='理论线', line=dict(color='red')),
                row=1, col=1
            )
        
        if 'model2' in self.model_results:
            residuals2 = np.array(self.model_results['model2']['residuals'])
            qq_data2 = stats.probplot(residuals2, dist="norm")
            
            fig_qq.add_trace(
                go.Scatter(x=qq_data2[0][0], y=qq_data2[0][1], mode='markers', name='夜场人次QQ'),
                row=1, col=2
            )
            
            # 添加理论线
            fig_qq.add_trace(
                go.Scatter(x=qq_data2[0][0], y=qq_data2[1][1] + qq_data2[1][0] * qq_data2[0][0], 
                          mode='lines', name='理论线', line=dict(color='red')),
                row=1, col=2
            )
        
        fig_qq.update_layout(
            title="残差正态性检验（QQ图）",
            height=400,
            showlegend=False
        )
        
        plots['qq_plot'] = fig_qq
        
        self.figures.update(plots)
        logger.info("回归分析图表创建完成")
        
        return plots
    
    def create_coefficient_plots(self) -> Dict[str, Any]:
        """
        创建回归系数图表
        
        Returns:
            回归系数图表字典
        """
        if not self.model_results:
            logger.warning("没有模型结果，跳过系数图表创建")
            return {}
        
        logger.info("创建回归系数图表...")
        
        plots = {}
        
        # 回归系数对比图
        fig_coef = make_subplots(
            rows=1, cols=2,
            subplot_titles=['大散人次模型系数', '夜场人次模型系数']
        )
        
        if 'model1' in self.model_results:
            coef_data1 = self.model_results['model1']['coefficients']
            features1 = [k for k in coef_data1.keys() if k != 'intercept']
            values1 = [coef_data1[k]['value'] for k in features1]
            errors1 = [coef_data1[k]['std_err'] for k in features1]
            
            fig_coef.add_trace(
                go.Bar(x=features1, y=values1, error_y=dict(type='data', array=errors1),
                      name='大散人次系数'),
                row=1, col=1
            )
        
        if 'model2' in self.model_results:
            coef_data2 = self.model_results['model2']['coefficients']
            features2 = [k for k in coef_data2.keys() if k != 'intercept']
            values2 = [coef_data2[k]['value'] for k in features2]
            errors2 = [coef_data2[k]['std_err'] for k in features2]
            
            fig_coef.add_trace(
                go.Bar(x=features2, y=values2, error_y=dict(type='data', array=errors2),
                      name='夜场人次系数'),
                row=1, col=2
            )
        
        fig_coef.update_layout(
            title="回归系数对比",
            height=500,
            showlegend=False
        )
        
        plots['coefficient_plot'] = fig_coef
        
        self.figures.update(plots)
        logger.info("回归系数图表创建完成")
        
        return plots
    
    def create_all_plots(self) -> Dict[str, Any]:
        """
        创建所有图表
        
        Returns:
            所有图表字典
        """
        logger.info("开始创建所有可视化图表...")
        
        all_plots = {}
        
        # 创建各类图表
        all_plots.update(self.create_descriptive_plots())
        all_plots.update(self.create_correlation_plots())
        all_plots.update(self.create_regression_plots())
        all_plots.update(self.create_coefficient_plots())
        
        logger.info(f"所有图表创建完成，共 {len(all_plots)} 个图表")
        
        return all_plots
    
    def save_plots_as_html(self, output_dir: str = ".") -> List[str]:
        """
        将所有图表保存为HTML文件
        
        Args:
            output_dir: 输出目录
        
        Returns:
            保存的文件路径列表
        """
        import os
        
        if not self.figures:
            logger.warning("没有图表可保存")
            return []
        
        saved_files = []
        
        for plot_name, fig in self.figures.items():
            file_path = os.path.join(output_dir, f"{plot_name}.html")
            fig.write_html(file_path)
            saved_files.append(file_path)
            logger.info(f"图表已保存: {file_path}")
        
        return saved_files
