#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
武汉项目对数变换回归分析模块
对目标变量进行对数变换后重新构建回归模型，并与原始模型进行对比
"""

import pandas as pd
import numpy as np
import statsmodels.api as sm
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from scipy import stats
from statsmodels.stats.diagnostic import het_breuschpagan
from statsmodels.stats.stattools import durbin_watson
from statsmodels.stats.outliers_influence import variance_inflation_factor
import logging
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LogTransformRegressionAnalyzer:
    """对数变换回归分析器"""
    
    def __init__(self, X: pd.DataFrame, y1: pd.Series, y2: pd.Series):
        """
        初始化对数变换回归分析器
        
        Args:
            X: 特征变量DataFrame
            y1: 目标变量1 (大散人次)
            y2: 目标变量2 (夜场人次)
        """
        self.X = X.copy()
        self.y1_original = y1.copy()
        self.y2_original = y2.copy()
        
        # 对数变换后的目标变量
        self.y1_log = np.log(y1)
        self.y2_log = np.log(y2)
        
        # 模型存储
        self.log_model1 = None  # 对数大散人次模型
        self.log_model2 = None  # 对数夜场人次模型
        self.log_model1_sm = None  # statsmodels对数模型1
        self.log_model2_sm = None  # statsmodels对数模型2
        
        # 结果存储
        self.log_results = {}
        
        logger.info(f"初始化对数变换回归分析器 - 特征数: {X.shape[1]}, 样本数: {len(y1)}")
        logger.info(f"对数变换范围 - ln(大散人次): [{self.y1_log.min():.4f}, {self.y1_log.max():.4f}]")
        logger.info(f"对数变换范围 - ln(夜场人次): [{self.y2_log.min():.4f}, {self.y2_log.max():.4f}]")
    
    def build_log_model1(self) -> Dict[str, Any]:
        """
        构建对数模型1：ln(大散人次)回归模型
        
        Returns:
            对数模型1的详细结果
        """
        logger.info("构建ln(大散人次)回归模型...")
        
        X = self.X
        y_log = self.y1_log
        y_original = self.y1_original
        
        # 添加常数项用于statsmodels
        X_with_const = sm.add_constant(X)
        
        # 使用statsmodels进行详细的统计分析
        self.log_model1_sm = sm.OLS(y_log, X_with_const).fit()
        
        # 使用sklearn进行预测
        self.log_model1 = LinearRegression()
        self.log_model1.fit(X, y_log)
        y_log_pred = self.log_model1.predict(X)
        
        # 将对数预测值转换回原始尺度
        y_pred_original = np.exp(y_log_pred)
        
        # 计算对数尺度的评估指标
        r2_log = r2_score(y_log, y_log_pred)
        adj_r2_log = 1 - (1 - r2_log) * (len(y_log) - 1) / (len(y_log) - X.shape[1] - 1)
        mse_log = mean_squared_error(y_log, y_log_pred)
        rmse_log = np.sqrt(mse_log)
        mae_log = mean_absolute_error(y_log, y_log_pred)
        
        # 计算原始尺度的评估指标
        r2_original = r2_score(y_original, y_pred_original)
        mse_original = mean_squared_error(y_original, y_pred_original)
        rmse_original = np.sqrt(mse_original)
        mae_original = mean_absolute_error(y_original, y_pred_original)
        
        # 残差分析（对数尺度）
        residuals_log = y_log - y_log_pred
        
        # 正态性检验
        shapiro_stat, shapiro_p = stats.shapiro(residuals_log)
        
        # 异方差检验
        bp_test = het_breuschpagan(residuals_log, X_with_const)
        
        # 自相关检验
        dw_stat = durbin_watson(residuals_log)
        
        # 多重共线性检验
        vif_data = pd.DataFrame()
        vif_data["特征"] = X.columns
        vif_data["VIF"] = [variance_inflation_factor(X.values, i) 
                          for i in range(X.shape[1])]
        
        # 整理结果
        log_model1_results = {
            'model_summary': {
                'r_squared_log': float(r2_log),
                'adj_r_squared_log': float(adj_r2_log),
                'r_squared_original': float(r2_original),
                'f_statistic': float(self.log_model1_sm.fvalue),
                'f_pvalue': float(self.log_model1_sm.f_pvalue),
                'mse_log': float(mse_log),
                'rmse_log': float(rmse_log),
                'mae_log': float(mae_log),
                'mse_original': float(mse_original),
                'rmse_original': float(rmse_original),
                'mae_original': float(mae_original),
                'aic': float(self.log_model1_sm.aic),
                'bic': float(self.log_model1_sm.bic)
            },
            'coefficients': {
                'intercept': {
                    'value': float(self.log_model1_sm.params['const']),
                    'std_err': float(self.log_model1_sm.bse['const']),
                    't_stat': float(self.log_model1_sm.tvalues['const']),
                    'p_value': float(self.log_model1_sm.pvalues['const']),
                    'conf_int_lower': float(self.log_model1_sm.conf_int().loc['const', 0]),
                    'conf_int_upper': float(self.log_model1_sm.conf_int().loc['const', 1])
                }
            },
            'residual_analysis': {
                'normality_test': {
                    'shapiro_stat': float(shapiro_stat),
                    'shapiro_p': float(shapiro_p)
                },
                'heteroscedasticity_test': {
                    'bp_statistic': float(bp_test[0]),
                    'bp_p_value': float(bp_test[1])
                },
                'autocorrelation_test': {
                    'durbin_watson': float(dw_stat)
                }
            },
            'multicollinearity': vif_data.to_dict('records'),
            'predictions_log': y_log_pred.tolist(),
            'predictions_original': y_pred_original.tolist(),
            'residuals_log': residuals_log.tolist(),
            'actual_values_log': y_log.tolist(),
            'actual_values_original': y_original.tolist()
        }
        
        # 添加各特征的系数信息
        for feature in X.columns:
            log_model1_results['coefficients'][feature] = {
                'value': float(self.log_model1_sm.params[feature]),
                'std_err': float(self.log_model1_sm.bse[feature]),
                't_stat': float(self.log_model1_sm.tvalues[feature]),
                'p_value': float(self.log_model1_sm.pvalues[feature]),
                'conf_int_lower': float(self.log_model1_sm.conf_int().loc[feature, 0]),
                'conf_int_upper': float(self.log_model1_sm.conf_int().loc[feature, 1])
            }
        
        self.log_results['log_model1'] = log_model1_results
        logger.info(f"ln(大散人次)模型构建完成 - 对数R²: {r2_log:.4f}, 原始R²: {r2_original:.4f}")
        
        return log_model1_results
    
    def build_log_model2(self) -> Dict[str, Any]:
        """
        构建对数模型2：ln(夜场人次)回归模型
        
        Returns:
            对数模型2的详细结果
        """
        logger.info("构建ln(夜场人次)回归模型...")
        
        X = self.X
        y_log = self.y2_log
        y_original = self.y2_original
        
        # 添加常数项用于statsmodels
        X_with_const = sm.add_constant(X)
        
        # 使用statsmodels进行详细的统计分析
        self.log_model2_sm = sm.OLS(y_log, X_with_const).fit()
        
        # 使用sklearn进行预测
        self.log_model2 = LinearRegression()
        self.log_model2.fit(X, y_log)
        y_log_pred = self.log_model2.predict(X)
        
        # 将对数预测值转换回原始尺度
        y_pred_original = np.exp(y_log_pred)
        
        # 计算对数尺度的评估指标
        r2_log = r2_score(y_log, y_log_pred)
        adj_r2_log = 1 - (1 - r2_log) * (len(y_log) - 1) / (len(y_log) - X.shape[1] - 1)
        mse_log = mean_squared_error(y_log, y_log_pred)
        rmse_log = np.sqrt(mse_log)
        mae_log = mean_absolute_error(y_log, y_log_pred)
        
        # 计算原始尺度的评估指标
        r2_original = r2_score(y_original, y_pred_original)
        mse_original = mean_squared_error(y_original, y_pred_original)
        rmse_original = np.sqrt(mse_original)
        mae_original = mean_absolute_error(y_original, y_pred_original)
        
        # 残差分析（对数尺度）
        residuals_log = y_log - y_log_pred
        
        # 正态性检验
        shapiro_stat, shapiro_p = stats.shapiro(residuals_log)
        
        # 异方差检验
        bp_test = het_breuschpagan(residuals_log, X_with_const)
        
        # 自相关检验
        dw_stat = durbin_watson(residuals_log)
        
        # 多重共线性检验
        vif_data = pd.DataFrame()
        vif_data["特征"] = X.columns
        vif_data["VIF"] = [variance_inflation_factor(X.values, i) 
                          for i in range(X.shape[1])]
        
        # 整理结果
        log_model2_results = {
            'model_summary': {
                'r_squared_log': float(r2_log),
                'adj_r_squared_log': float(adj_r2_log),
                'r_squared_original': float(r2_original),
                'f_statistic': float(self.log_model2_sm.fvalue),
                'f_pvalue': float(self.log_model2_sm.f_pvalue),
                'mse_log': float(mse_log),
                'rmse_log': float(rmse_log),
                'mae_log': float(mae_log),
                'mse_original': float(mse_original),
                'rmse_original': float(rmse_original),
                'mae_original': float(mae_original),
                'aic': float(self.log_model2_sm.aic),
                'bic': float(self.log_model2_sm.bic)
            },
            'coefficients': {
                'intercept': {
                    'value': float(self.log_model2_sm.params['const']),
                    'std_err': float(self.log_model2_sm.bse['const']),
                    't_stat': float(self.log_model2_sm.tvalues['const']),
                    'p_value': float(self.log_model2_sm.pvalues['const']),
                    'conf_int_lower': float(self.log_model2_sm.conf_int().loc['const', 0]),
                    'conf_int_upper': float(self.log_model2_sm.conf_int().loc['const', 1])
                }
            },
            'residual_analysis': {
                'normality_test': {
                    'shapiro_stat': float(shapiro_stat),
                    'shapiro_p': float(shapiro_p)
                },
                'heteroscedasticity_test': {
                    'bp_statistic': float(bp_test[0]),
                    'bp_p_value': float(bp_test[1])
                },
                'autocorrelation_test': {
                    'durbin_watson': float(dw_stat)
                }
            },
            'multicollinearity': vif_data.to_dict('records'),
            'predictions_log': y_log_pred.tolist(),
            'predictions_original': y_pred_original.tolist(),
            'residuals_log': residuals_log.tolist(),
            'actual_values_log': y_log.tolist(),
            'actual_values_original': y_original.tolist()
        }
        
        # 添加各特征的系数信息
        for feature in X.columns:
            log_model2_results['coefficients'][feature] = {
                'value': float(self.log_model2_sm.params[feature]),
                'std_err': float(self.log_model2_sm.bse[feature]),
                't_stat': float(self.log_model2_sm.tvalues[feature]),
                'p_value': float(self.log_model2_sm.pvalues[feature]),
                'conf_int_lower': float(self.log_model2_sm.conf_int().loc[feature, 0]),
                'conf_int_upper': float(self.log_model2_sm.conf_int().loc[feature, 1])
            }
        
        self.log_results['log_model2'] = log_model2_results
        logger.info(f"ln(夜场人次)模型构建完成 - 对数R²: {r2_log:.4f}, 原始R²: {r2_original:.4f}")
        
        return log_model2_results
    
    def run_log_analysis(self) -> Dict[str, Any]:
        """
        执行完整的对数变换回归分析
        
        Returns:
            完整的对数分析结果
        """
        logger.info("开始执行对数变换回归分析...")
        
        # 构建两个对数模型
        self.build_log_model1()
        self.build_log_model2()
        
        logger.info("对数变换回归分析完成")
        
        return self.log_results
    
    def get_log_model_summary(self) -> str:
        """
        获取对数模型摘要文本
        
        Returns:
            对数模型摘要字符串
        """
        if not self.log_results:
            return "尚未构建对数模型"
        
        summary_lines = []
        summary_lines.append("=== 武汉项目对数变换回归分析摘要 ===\n")
        
        if 'log_model1' in self.log_results:
            model1 = self.log_results['log_model1']['model_summary']
            summary_lines.append("【ln(大散人次)回归模型】")
            summary_lines.append(f"  对数尺度R² = {model1['r_squared_log']:.4f}")
            summary_lines.append(f"  对数尺度调整R² = {model1['adj_r_squared_log']:.4f}")
            summary_lines.append(f"  原始尺度R² = {model1['r_squared_original']:.4f}")
            summary_lines.append(f"  F统计量 = {model1['f_statistic']:.2f} (p = {model1['f_pvalue']:.4f})")
            summary_lines.append(f"  原始尺度RMSE = {model1['rmse_original']:.2f}")
        
        if 'log_model2' in self.log_results:
            model2 = self.log_results['log_model2']['model_summary']
            summary_lines.append("\n【ln(夜场人次)回归模型】")
            summary_lines.append(f"  对数尺度R² = {model2['r_squared_log']:.4f}")
            summary_lines.append(f"  对数尺度调整R² = {model2['adj_r_squared_log']:.4f}")
            summary_lines.append(f"  原始尺度R² = {model2['r_squared_original']:.4f}")
            summary_lines.append(f"  F统计量 = {model2['f_statistic']:.2f} (p = {model2['f_pvalue']:.4f})")
            summary_lines.append(f"  原始尺度RMSE = {model2['rmse_original']:.2f}")
        
        return "\n".join(summary_lines)
