#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
武汉项目HTML报告生成模块
负责将所有分析结果整合成完整的HTML报告
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json
import logging
from typing import Dict, List, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HTMLReportGenerator:
    """HTML报告生成器"""
    
    def __init__(self, data: pd.DataFrame, analysis_results: Dict, model_results: Dict, plots: Dict):
        """
        初始化HTML报告生成器
        
        Args:
            data: 原始数据
            analysis_results: 探索性分析结果
            model_results: 回归模型结果
            plots: 可视化图表
        """
        self.data = data
        self.analysis_results = analysis_results
        self.model_results = model_results
        self.plots = plots
        self.report_html = ""
        
        logger.info("初始化HTML报告生成器")
    
    def generate_header(self) -> str:
        """生成HTML报告头部"""
        header = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>武汉项目暑期数据多元线性回归分析报告</title>
            <style>
                body {{
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    line-height: 1.6;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                }}
                h1 {{
                    color: #2c3e50;
                    text-align: center;
                    border-bottom: 3px solid #3498db;
                    padding-bottom: 10px;
                }}
                h2 {{
                    color: #34495e;
                    border-left: 4px solid #3498db;
                    padding-left: 15px;
                    margin-top: 30px;
                }}
                h3 {{
                    color: #7f8c8d;
                    margin-top: 25px;
                }}
                .summary-box {{
                    background-color: #ecf0f1;
                    padding: 20px;
                    border-radius: 8px;
                    margin: 20px 0;
                    border-left: 5px solid #3498db;
                }}
                .metric-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 20px;
                    margin: 20px 0;
                }}
                .metric-card {{
                    background-color: #ffffff;
                    padding: 15px;
                    border-radius: 8px;
                    border: 1px solid #bdc3c7;
                    text-align: center;
                }}
                .metric-value {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #2980b9;
                }}
                .metric-label {{
                    color: #7f8c8d;
                    font-size: 14px;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                }}
                th, td {{
                    border: 1px solid #bdc3c7;
                    padding: 12px;
                    text-align: left;
                }}
                th {{
                    background-color: #3498db;
                    color: white;
                }}
                tr:nth-child(even) {{
                    background-color: #f8f9fa;
                }}
                .plot-container {{
                    margin: 30px 0;
                    text-align: center;
                }}
                .significance {{
                    font-weight: bold;
                }}
                .significant {{
                    color: #27ae60;
                }}
                .not-significant {{
                    color: #e74c3c;
                }}
                .footer {{
                    margin-top: 50px;
                    text-align: center;
                    color: #7f8c8d;
                    border-top: 1px solid #bdc3c7;
                    padding-top: 20px;
                }}
            </style>
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        </head>
        <body>
            <div class="container">
                <h1>武汉项目暑期数据多元线性回归分析报告</h1>
                <div class="summary-box">
                    <p><strong>报告生成时间：</strong>{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
                    <p><strong>分析目标：</strong>基于历年武汉项目暑期数据，建立多元线性回归模型分析大散人次和夜场人次的影响因素</p>
                    <p><strong>数据样本：</strong>{len(self.data)}条记录，涵盖{len(self.data.columns)}个变量</p>
                </div>
        """
        return header
    
    def generate_data_overview(self) -> str:
        """生成数据概览部分"""
        logger.info("生成数据概览部分...")
        
        overview = """
        <h2>1. 数据概览</h2>
        """
        
        # 基本统计信息
        if 'descriptive_stats' in self.analysis_results:
            stats = self.analysis_results['descriptive_stats']
            
            overview += """
            <h3>1.1 基本统计信息</h3>
            <div class="metric-grid">
            """
            
            # 目标变量统计
            if 'target_variables' in stats:
                for var, var_stats in stats['target_variables'].items():
                    overview += f"""
                    <div class="metric-card">
                        <div class="metric-value">{var_stats['均值']:.1f}</div>
                        <div class="metric-label">{var}平均值</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{var_stats['标准差']:.1f}</div>
                        <div class="metric-label">{var}标准差</div>
                    </div>
                    """
            
            overview += "</div>"
            
            # 详细统计表格
            if 'target_variables' in stats:
                overview += """
                <h3>1.2 目标变量详细统计</h3>
                <table>
                    <tr>
                        <th>变量</th>
                        <th>样本数</th>
                        <th>均值</th>
                        <th>标准差</th>
                        <th>最小值</th>
                        <th>中位数</th>
                        <th>最大值</th>
                        <th>偏度</th>
                        <th>峰度</th>
                        <th>变异系数</th>
                    </tr>
                """
                
                for var, var_stats in stats['target_variables'].items():
                    overview += f"""
                    <tr>
                        <td>{var}</td>
                        <td>{var_stats['样本数']}</td>
                        <td>{var_stats['均值']:.2f}</td>
                        <td>{var_stats['标准差']:.2f}</td>
                        <td>{var_stats['最小值']:.2f}</td>
                        <td>{var_stats['中位数']:.2f}</td>
                        <td>{var_stats['最大值']:.2f}</td>
                        <td>{var_stats['偏度']:.3f}</td>
                        <td>{var_stats['峰度']:.3f}</td>
                        <td>{var_stats['变异系数']:.3f}</td>
                    </tr>
                    """
                
                overview += "</table>"
        
        return overview
    
    def generate_correlation_analysis(self) -> str:
        """生成相关性分析部分"""
        logger.info("生成相关性分析部分...")
        
        correlation = """
        <h2>2. 相关性分析</h2>
        """
        
        if 'correlation_analysis' in self.analysis_results:
            corr_data = self.analysis_results['correlation_analysis']
            
            if 'target_feature_correlations' in corr_data:
                correlation += """
                <h3>2.1 目标变量与特征变量相关性</h3>
                <table>
                    <tr>
                        <th>目标变量</th>
                        <th>特征变量</th>
                        <th>皮尔逊相关系数</th>
                        <th>p值</th>
                        <th>显著性</th>
                        <th>斯皮尔曼相关系数</th>
                        <th>p值</th>
                    </tr>
                """
                
                for target, features in corr_data['target_feature_correlations'].items():
                    for feature, corr_info in features.items():
                        pearson_sig = "显著" if corr_info['pearson_p_value'] < 0.05 else "不显著"
                        sig_class = "significant" if corr_info['pearson_p_value'] < 0.05 else "not-significant"
                        
                        correlation += f"""
                        <tr>
                            <td>{target}</td>
                            <td>{feature}</td>
                            <td>{corr_info['pearson_r']:.4f}</td>
                            <td>{corr_info['pearson_p_value']:.4f}</td>
                            <td class="significance {sig_class}">{pearson_sig}</td>
                            <td>{corr_info['spearman_r']:.4f}</td>
                            <td>{corr_info['spearman_p_value']:.4f}</td>
                        </tr>
                        """
                
                correlation += "</table>"
        
        return correlation
    
    def generate_model_results(self) -> str:
        """生成模型结果部分"""
        logger.info("生成模型结果部分...")
        
        models = """
        <h2>3. 多元线性回归模型结果</h2>
        """
        
        if self.model_results:
            # 模型比较
            if 'model_comparison' in self.model_results:
                comparison = self.model_results['model_comparison']
                
                models += """
                <h3>3.1 模型性能对比</h3>
                <table>
                    <tr>
                        <th>模型</th>
                        <th>R²</th>
                        <th>调整R²</th>
                        <th>RMSE</th>
                        <th>MAE</th>
                        <th>AIC</th>
                        <th>BIC</th>
                        <th>F统计量</th>
                        <th>F检验p值</th>
                        <th>模型显著性</th>
                    </tr>
                """
                
                for model_name, metrics in comparison['performance_metrics'].items():
                    significance_info = comparison['model_significance'][model_name]
                    sig_class = "significant" if significance_info['F检验p值'] < 0.05 else "not-significant"
                    
                    models += f"""
                    <tr>
                        <td>{model_name}</td>
                        <td>{metrics['R²']:.4f}</td>
                        <td>{metrics['调整R²']:.4f}</td>
                        <td>{metrics['RMSE']:.2f}</td>
                        <td>{metrics['MAE']:.2f}</td>
                        <td>{metrics['AIC']:.2f}</td>
                        <td>{metrics['BIC']:.2f}</td>
                        <td>{significance_info['F统计量']:.2f}</td>
                        <td>{significance_info['F检验p值']:.4f}</td>
                        <td class="significance {sig_class}">{significance_info['模型显著性']}</td>
                    </tr>
                    """
                
                models += "</table>"
            
            # 详细系数分析
            model_names = ['model1', 'model2']
            model_titles = ['大散人次回归模型', '夜场人次回归模型']
            
            for i, (model_key, model_title) in enumerate(zip(model_names, model_titles)):
                if model_key in self.model_results:
                    model_data = self.model_results[model_key]
                    
                    models += f"""
                    <h3>3.{i+2} {model_title}</h3>
                    """
                    
                    # 模型摘要
                    summary = model_data['model_summary']
                    models += f"""
                    <div class="summary-box">
                        <p><strong>模型拟合度：</strong>R² = {summary['r_squared']:.4f}, 调整R² = {summary['adj_r_squared']:.4f}</p>
                        <p><strong>模型显著性：</strong>F({len(model_data['coefficients'])-1}, {len(model_data['actual_values'])-len(model_data['coefficients'])}) = {summary['f_statistic']:.2f}, p = {summary['f_pvalue']:.4f}</p>
                        <p><strong>预测精度：</strong>RMSE = {summary['rmse']:.2f}, MAE = {summary['mae']:.2f}</p>
                    </div>
                    """
                    
                    # 回归系数表
                    models += """
                    <h4>回归系数详情</h4>
                    <table>
                        <tr>
                            <th>变量</th>
                            <th>系数</th>
                            <th>标准误</th>
                            <th>t统计量</th>
                            <th>p值</th>
                            <th>显著性</th>
                            <th>95%置信区间</th>
                        </tr>
                    """
                    
                    for var, coef_info in model_data['coefficients'].items():
                        var_name = "常数项" if var == "intercept" else var
                        significance = "显著" if coef_info['p_value'] < 0.05 else "不显著"
                        sig_class = "significant" if coef_info['p_value'] < 0.05 else "not-significant"
                        
                        models += f"""
                        <tr>
                            <td>{var_name}</td>
                            <td>{coef_info['value']:.4f}</td>
                            <td>{coef_info['std_err']:.4f}</td>
                            <td>{coef_info['t_stat']:.3f}</td>
                            <td>{coef_info['p_value']:.4f}</td>
                            <td class="significance {sig_class}">{significance}</td>
                            <td>[{coef_info['conf_int_lower']:.4f}, {coef_info['conf_int_upper']:.4f}]</td>
                        </tr>
                        """
                    
                    models += "</table>"
                    
                    # 模型诊断
                    residual_analysis = model_data['residual_analysis']
                    models += f"""
                    <h4>模型诊断</h4>
                    <div class="summary-box">
                        <p><strong>残差正态性检验：</strong>Shapiro-Wilk统计量 = {residual_analysis['normality_test']['shapiro_stat']:.4f}, p = {residual_analysis['normality_test']['shapiro_p']:.4f}</p>
                        <p><strong>异方差检验：</strong>Breusch-Pagan统计量 = {residual_analysis['heteroscedasticity_test']['bp_statistic']:.4f}, p = {residual_analysis['heteroscedasticity_test']['bp_p_value']:.4f}</p>
                        <p><strong>自相关检验：</strong>Durbin-Watson统计量 = {residual_analysis['autocorrelation_test']['durbin_watson']:.4f}</p>
                    </div>
                    """
                    
                    # 多重共线性检验
                    if 'multicollinearity' in model_data:
                        models += """
                        <h4>多重共线性检验（VIF）</h4>
                        <table>
                            <tr>
                                <th>特征</th>
                                <th>VIF值</th>
                                <th>共线性程度</th>
                            </tr>
                        """
                        
                        for vif_info in model_data['multicollinearity']:
                            vif_value = vif_info['VIF']
                            if vif_value < 5:
                                collinearity = "低"
                            elif vif_value < 10:
                                collinearity = "中等"
                            else:
                                collinearity = "高"
                            
                            models += f"""
                            <tr>
                                <td>{vif_info['特征']}</td>
                                <td>{vif_value:.2f}</td>
                                <td>{collinearity}</td>
                            </tr>
                            """
                        
                        models += "</table>"
        
        return models
    
    def generate_plots_section(self) -> str:
        """生成图表部分"""
        logger.info("生成图表部分...")
        
        plots_section = """
        <h2>4. 数据可视化分析</h2>
        """
        
        # 为每个图表生成HTML
        plot_titles = {
            'target_distribution': '4.1 目标变量分布分析',
            'feature_distribution': '4.2 特征变量分布分析',
            'weekday_effect': '4.3 星期效应分析',
            'correlation_heatmap': '4.4 变量相关性热力图',
            'scatter_matrix': '4.5 变量散点图矩阵',
            'target_feature_scatter': '4.6 目标变量与主要特征散点图',
            'prediction_plot': '4.7 回归模型预测效果',
            'residual_plot': '4.8 残差分析',
            'qq_plot': '4.9 残差正态性检验（QQ图）',
            'coefficient_plot': '4.10 回归系数对比'
        }
        
        for plot_key, plot_title in plot_titles.items():
            if plot_key in self.plots:
                plots_section += f"""
                <h3>{plot_title}</h3>
                <div class="plot-container" id="{plot_key}">
                </div>
                <script>
                    var plotData_{plot_key} = {self.plots[plot_key].to_json()};
                    Plotly.newPlot('{plot_key}', plotData_{plot_key}.data, plotData_{plot_key}.layout);
                </script>
                """
        
        return plots_section
    
    def generate_conclusions(self) -> str:
        """生成结论部分"""
        logger.info("生成结论部分...")
        
        conclusions = """
        <h2>5. 分析结论与建议</h2>
        """
        
        if self.model_results and 'model_comparison' in self.model_results:
            comparison = self.model_results['model_comparison']
            
            conclusions += """
            <h3>5.1 主要发现</h3>
            <div class="summary-box">
            """
            
            # 模型性能总结
            for model_name, metrics in comparison['performance_metrics'].items():
                significance_info = comparison['model_significance'][model_name]
                
                conclusions += f"""
                <p><strong>{model_name}：</strong></p>
                <ul>
                    <li>模型解释力：R² = {metrics['R²']:.1%}，调整R² = {metrics['调整R²']:.1%}</li>
                    <li>预测精度：RMSE = {metrics['RMSE']:.1f}</li>
                    <li>模型显著性：{significance_info['模型显著性']}（p = {significance_info['F检验p值']:.4f}）</li>
                </ul>
                """
            
            conclusions += """
            </div>
            
            <h3>5.2 管理建议</h3>
            <div class="summary-box">
                <ol>
                    <li><strong>价格策略优化：</strong>根据回归系数分析客单价对人次的影响，制定合理的定价策略</li>
                    <li><strong>星期效应利用：</strong>基于星期变量的显著性，针对不同星期制定差异化营销策略</li>
                    <li><strong>暑期进程管理：</strong>考虑Holiday_Day_Index的影响，在暑期不同阶段调整运营重点</li>
                    <li><strong>预测模型应用：</strong>利用建立的回归模型进行人次预测，支持运营决策</li>
                </ol>
            </div>
            
            <h3>5.3 模型局限性</h3>
            <div class="summary-box">
                <ul>
                    <li>模型基于历史数据，可能不能完全反映未来趋势变化</li>
                    <li>未考虑天气、竞争对手等外部因素的影响</li>
                    <li>线性假设可能不完全适用于所有变量关系</li>
                    <li>建议定期更新模型以保持预测准确性</li>
                </ul>
            </div>
            """
        
        return conclusions
    
    def generate_footer(self) -> str:
        """生成HTML报告尾部"""
        footer = f"""
                <div class="footer">
                    <p>本报告由武汉项目数据分析系统自动生成</p>
                    <p>生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
                    <p>数据来源：data_wuhan.db | 分析方法：多元线性回归</p>
                </div>
            </div>
        </body>
        </html>
        """
        return footer
    
    def generate_full_report(self) -> str:
        """生成完整的HTML报告"""
        logger.info("开始生成完整的HTML报告...")
        
        # 组装完整报告
        self.report_html = ""
        self.report_html += self.generate_header()
        self.report_html += self.generate_data_overview()
        self.report_html += self.generate_correlation_analysis()
        self.report_html += self.generate_model_results()
        self.report_html += self.generate_plots_section()
        self.report_html += self.generate_conclusions()
        self.report_html += self.generate_footer()
        
        logger.info("HTML报告生成完成")
        
        return self.report_html
    
    def save_report(self, filename: str = "武汉项目回归分析报告.html") -> str:
        """
        保存HTML报告到文件
        
        Args:
            filename: 输出文件名
        
        Returns:
            保存的文件路径
        """
        if not self.report_html:
            self.generate_full_report()
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.report_html)
            
            logger.info(f"HTML报告已保存到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存报告失败: {e}")
            raise
