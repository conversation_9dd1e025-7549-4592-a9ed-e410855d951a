#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查对数变换的可行性
检查目标变量是否存在零值或负值
"""

import sqlite3
import pandas as pd
import numpy as np

def check_log_feasibility():
    """检查对数变换的可行性"""
    print("=" * 60)
    print("检查对数变换的可行性")
    print("=" * 60)
    
    # 连接数据库
    conn = sqlite3.connect('data_wuhan.db')
    
    # 获取表名
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    table_name = tables[0][0]
    
    # 加载数据
    data = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
    conn.close()
    
    print(f"数据基本信息:")
    print(f"  - 总样本数: {len(data)}")
    print(f"  - 数据列: {list(data.columns)}")
    
    # 检查目标变量
    target_vars = ['大散人次', '夜场人次']
    
    for var in target_vars:
        if var in data.columns:
            values = data[var]
            print(f"\n{var} 数据检查:")
            print(f"  - 最小值: {values.min()}")
            print(f"  - 最大值: {values.max()}")
            print(f"  - 均值: {values.mean():.2f}")
            print(f"  - 零值数量: {(values == 0).sum()}")
            print(f"  - 负值数量: {(values < 0).sum()}")
            print(f"  - 缺失值数量: {values.isnull().sum()}")
            
            # 检查是否可以直接取对数
            if values.min() > 0:
                print(f"  - 可以直接取对数: 是")
                log_values = np.log(values)
                print(f"  - ln({var}) 范围: [{log_values.min():.4f}, {log_values.max():.4f}]")
            else:
                print(f"  - 可以直接取对数: 否")
                print(f"  - 建议使用 ln({var} + 1) 变换")
                log_values = np.log(values + 1)
                print(f"  - ln({var} + 1) 范围: [{log_values.min():.4f}, {log_values.max():.4f}]")
    
    print(f"\n" + "=" * 60)
    print("检查完成")
    print("=" * 60)

if __name__ == "__main__":
    check_log_feasibility()
