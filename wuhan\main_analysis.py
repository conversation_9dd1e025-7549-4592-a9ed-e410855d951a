#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
武汉项目暑期数据多元线性回归分析主程序
整合所有分析模块，执行完整的分析流程并生成HTML报告
"""

import sys
import os
import logging
import traceback
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from data_preprocessing import WuhanDataPreprocessor
from exploratory_analysis import ExploratoryDataAnalyzer
from regression_modeling import MultipleLinearRegressionAnalyzer
from visualization import DataVisualizationGenerator
from report_generator import HTMLReportGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class WuhanRegressionAnalysis:
    """武汉项目回归分析主控制器"""
    
    def __init__(self, db_path: str = 'data_wuhan.db'):
        """
        初始化分析控制器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.preprocessor = None
        self.analyzer = None
        self.modeler = None
        self.visualizer = None
        self.report_generator = None
        
        # 存储分析结果
        self.raw_data = None
        self.processed_data = None
        self.analysis_results = {}
        self.model_results = {}
        self.plots = {}
        
        logger.info("=" * 80)
        logger.info("武汉项目暑期数据多元线性回归分析系统启动")
        logger.info("=" * 80)
        logger.info(f"数据库路径: {db_path}")
        logger.info(f"分析开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def step1_data_preprocessing(self):
        """步骤1：数据预处理"""
        logger.info("\n" + "=" * 50)
        logger.info("步骤1：数据预处理")
        logger.info("=" * 50)
        
        try:
            # 初始化数据预处理器
            self.preprocessor = WuhanDataPreprocessor(self.db_path)
            
            # 加载原始数据
            logger.info("1.1 加载原始数据...")
            self.raw_data = self.preprocessor.load_data()
            
            # 验证数据结构
            logger.info("1.2 验证数据结构...")
            validation_results = self.preprocessor.validate_data_structure()
            logger.info(f"数据验证完成 - 总行数: {validation_results['total_rows']}, 总列数: {validation_results['total_columns']}")
            
            # 检测异常值
            logger.info("1.3 检测异常值...")
            outlier_results = self.preprocessor.detect_outliers()
            outlier_count = sum(info['outlier_count'] for info in outlier_results.values())
            logger.info(f"异常值检测完成 - 发现 {outlier_count} 个异常值")
            
            # 执行数据预处理
            logger.info("1.4 执行数据预处理...")
            self.processed_data = self.preprocessor.preprocess_data(remove_outliers=False)
            
            # 获取建模数据
            logger.info("1.5 准备建模数据...")
            X, y1, y2 = self.preprocessor.get_model_data()
            
            logger.info(f"数据预处理完成 - 特征数: {X.shape[1]}, 样本数: {len(y1)}")
            logger.info(f"特征变量: {list(X.columns)}")
            
            return X, y1, y2
            
        except Exception as e:
            logger.error(f"数据预处理失败: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def step2_exploratory_analysis(self):
        """步骤2：探索性数据分析"""
        logger.info("\n" + "=" * 50)
        logger.info("步骤2：探索性数据分析")
        logger.info("=" * 50)
        
        try:
            # 初始化探索性分析器
            self.analyzer = ExploratoryDataAnalyzer(self.processed_data)
            
            # 执行完整的探索性分析
            logger.info("2.1 执行描述性统计分析...")
            self.analysis_results = self.analyzer.run_full_analysis()
            
            # 输出分析摘要
            logger.info("2.2 生成分析摘要...")
            summary = self.analyzer.get_analysis_summary()
            logger.info(f"\n{summary}")
            
            logger.info("探索性数据分析完成")
            
        except Exception as e:
            logger.error(f"探索性数据分析失败: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def step3_regression_modeling(self, X, y1, y2):
        """步骤3：回归建模"""
        logger.info("\n" + "=" * 50)
        logger.info("步骤3：多元线性回归建模")
        logger.info("=" * 50)
        
        try:
            # 初始化回归分析器
            self.modeler = MultipleLinearRegressionAnalyzer(X, y1, y2)
            
            # 执行完整的回归分析
            logger.info("3.1 构建回归模型...")
            self.model_results = self.modeler.run_full_analysis()
            
            # 输出模型摘要
            logger.info("3.2 生成模型摘要...")
            summary = self.modeler.get_model_summary()
            logger.info(f"\n{summary}")
            
            logger.info("回归建模完成")
            
        except Exception as e:
            logger.error(f"回归建模失败: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def step4_visualization(self):
        """步骤4：数据可视化"""
        logger.info("\n" + "=" * 50)
        logger.info("步骤4：数据可视化")
        logger.info("=" * 50)
        
        try:
            # 初始化可视化生成器
            self.visualizer = DataVisualizationGenerator(
                self.processed_data, 
                self.analysis_results, 
                self.model_results
            )
            
            # 创建所有图表
            logger.info("4.1 创建可视化图表...")
            self.plots = self.visualizer.create_all_plots()
            
            logger.info(f"数据可视化完成 - 共生成 {len(self.plots)} 个图表")
            
        except Exception as e:
            logger.error(f"数据可视化失败: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def step5_generate_report(self, output_filename: str = "武汉项目回归分析报告.html"):
        """步骤5：生成HTML报告"""
        logger.info("\n" + "=" * 50)
        logger.info("步骤5：生成HTML报告")
        logger.info("=" * 50)
        
        try:
            # 初始化报告生成器
            self.report_generator = HTMLReportGenerator(
                self.processed_data,
                self.analysis_results,
                self.model_results,
                self.plots
            )
            
            # 生成并保存报告
            logger.info("5.1 生成HTML报告...")
            report_path = self.report_generator.save_report(output_filename)
            
            logger.info(f"HTML报告生成完成: {report_path}")
            
            return report_path
            
        except Exception as e:
            logger.error(f"报告生成失败: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def run_full_analysis(self, output_filename: str = "武汉项目回归分析报告.html"):
        """执行完整的分析流程"""
        start_time = datetime.now()
        
        try:
            logger.info("开始执行完整的回归分析流程...")
            
            # 步骤1：数据预处理
            X, y1, y2 = self.step1_data_preprocessing()
            
            # 步骤2：探索性数据分析
            self.step2_exploratory_analysis()
            
            # 步骤3：回归建模
            self.step3_regression_modeling(X, y1, y2)
            
            # 步骤4：数据可视化
            self.step4_visualization()
            
            # 步骤5：生成报告
            report_path = self.step5_generate_report(output_filename)
            
            # 计算总耗时
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.info("\n" + "=" * 80)
            logger.info("分析流程执行完成！")
            logger.info("=" * 80)
            logger.info(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"总耗时: {duration}")
            logger.info(f"报告文件: {report_path}")
            logger.info("=" * 80)
            
            return {
                'success': True,
                'report_path': report_path,
                'duration': str(duration),
                'summary': {
                    'data_samples': len(self.processed_data),
                    'features': len(X.columns) if 'X' in locals() else 0,
                    'model1_r2': self.model_results.get('model1', {}).get('model_summary', {}).get('r_squared', 0),
                    'model2_r2': self.model_results.get('model2', {}).get('model_summary', {}).get('r_squared', 0),
                    'plots_count': len(self.plots)
                }
            }
            
        except Exception as e:
            end_time = datetime.now()
            duration = end_time - start_time
            
            logger.error("\n" + "=" * 80)
            logger.error("分析流程执行失败！")
            logger.error("=" * 80)
            logger.error(f"错误信息: {e}")
            logger.error(f"执行时间: {duration}")
            logger.error("=" * 80)
            logger.error(traceback.format_exc())
            
            return {
                'success': False,
                'error': str(e),
                'duration': str(duration)
            }

def main():
    """主函数"""
    try:
        # 检查数据库文件是否存在
        db_path = 'data_wuhan.db'
        if not os.path.exists(db_path):
            logger.error(f"数据库文件不存在: {db_path}")
            logger.error("请确保数据库文件在当前目录下")
            return
        
        # 创建分析实例并执行
        analysis = WuhanRegressionAnalysis(db_path)
        result = analysis.run_full_analysis()
        
        if result['success']:
            print(f"\n✅ 分析完成！报告已生成: {result['report_path']}")
            print(f"📊 数据样本: {result['summary']['data_samples']}")
            print(f"📈 大散人次模型R²: {result['summary']['model1_r2']:.4f}")
            print(f"📈 夜场人次模型R²: {result['summary']['model2_r2']:.4f}")
            print(f"🎨 生成图表: {result['summary']['plots_count']}个")
        else:
            print(f"\n❌ 分析失败: {result['error']}")
            
    except KeyboardInterrupt:
        logger.info("用户中断分析流程")
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
